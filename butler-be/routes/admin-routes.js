import express from "express";
import {
  getAllOutlets,
  createOutlet,
  updateOutlet,
  deleteOutlet,
  createCategory,
  updateCategory,
  getAllCategories,
  deleteCategory,
  updateTheme,
  getDish,
  createDish,
  updateDish,
  deleteDish,
  updateDishes,
  getAllUserRequests,
  getSingleOutlet,
  removeDishFromOutlet,
  updateOrderStatus,
  updateOrderPriority,
  assignOrderToStaff,
  addKitchenNotes,
  createAdminOrder,
  getDashboardAnalytics,
  getCustomers,
  getAdminProfile,
  updateAdminProfile,
  changeAdminPassword,
  getAllAdmins,
  getCustomerDetails,
  updateCustomerDetails,
  updateCustomerStatus,
  updateOutletStatus,
  createCustomer,
  resetCustomerPassword,
  addStaffToOutlet,
  getAllEmployees,
  createEmployee,
  updateEmployee,
  deleteEmployee,
  resetEmployeePassword,
  removeStaffFromOutlet,
  deleteOrder,
  sendOrderInvoice,
  updateOrderPaymentStatus,
  markDishAsServed,
  updateAdminOrderItems,
} from "../controllers/admin-controller.js";
import { getDishes } from "../controllers/user-controller.js";
import { authenticateToken } from "../middlewares/auth.js";
import {
  getAllInventoryItems,
  getInventoryItem,
  createInventoryItem,
  updateInventoryItem,
  deleteInventoryItem,
  updateInventoryQuantity,
  getInventoryTransactions,
  getInventoryCategories,
  getLowStockItems,
  linkInventoryToDish,
  getInventorySummary,
} from "../controllers/inventory-controller.js";
import {
  registerFoodChainAdmin,
  getFoodChainById,
  updateFoodChain,
} from "../controllers/super-admin-controller.js";
import {
  generateEmbeddings,
  generateDishEmbedding,
  getSimilarDishes,
  getEmbeddingStats,
} from "../controllers/vector-controller.js";

const router = express.Router();

// food chain routes
router.get("/admin/food-chain/:id", authenticateToken, getFoodChainById);
router.put("/admin/food-chain/:id", authenticateToken, updateFoodChain);
router.post("/admin/register", authenticateToken, registerFoodChainAdmin);

// Outlet routes
router.get("/admin/outlets", authenticateToken, getAllOutlets);
router.post("/admin/create-outlet", authenticateToken, createOutlet);
router.put("/admin/outlets/:id", authenticateToken, updateOutlet);
router.delete("/admin/outlets/:id", authenticateToken, deleteOutlet);
router.get("/admin/outlets/:id", authenticateToken, getSingleOutlet);
router.put("/admin/outlets/:id/status", authenticateToken, updateOutletStatus);
router.post("/admin/outlets/:id/staff", authenticateToken, addStaffToOutlet);
router.delete(
  "/admin/outlets/:id/staff/:staffId",
  authenticateToken,
  removeStaffFromOutlet
);

// get dishes

// Category routes
router.get("/admin/categories", authenticateToken, getAllCategories);
router.post("/admin/categories", authenticateToken, createCategory);
router.put("/admin/categories/:id", authenticateToken, updateCategory);
router.delete("/admin/categories/:id", authenticateToken, deleteCategory);

// Theme routes
router.post("/admin/theme", authenticateToken, updateTheme);

// Dish routes
router.get("/admin/dishes/:id", authenticateToken, getDish);
router.post("/admin/create-dish", authenticateToken, createDish);
router.get("/admin/get-dishes", authenticateToken, getDishes);
router.put("/admin/dishes/:id", authenticateToken, updateDish);
router.put("/admin/dishes", authenticateToken, updateDishes);
router.delete("/admin/dishes/:id", authenticateToken, deleteDish);
router.post(
  "/admin/remove-dish-from-outlet",
  authenticateToken,
  removeDishFromOutlet
);

router.post(
  "/admin/add-dish-to-outlet",
  authenticateToken,
  removeDishFromOutlet
);

// Order routes
router.put(
  "/admin/orders/:orderId/status",
  authenticateToken,
  updateOrderStatus
);
router.put(
  "/admin/orders/:orderId/payment-status",
  authenticateToken,
  updateOrderPaymentStatus
);
router.put(
  "/admin/orders/:orderId/priority",
  authenticateToken,
  updateOrderPriority
);
router.post(
  "/admin/orders/:orderId/assign",
  authenticateToken,
  assignOrderToStaff
);
router.post(
  "/admin/orders/:orderId/kitchen-notes",
  authenticateToken,
  addKitchenNotes
);

// Admin order creation and deletion
router.post("/admin/orders/create", authenticateToken, createAdminOrder);
router.delete("/admin/orders/:orderId", authenticateToken, deleteOrder);

// Send invoice email for an order
router.post(
  "/admin/orders/:orderId/send-invoice",
  authenticateToken,
  sendOrderInvoice
);

// Mark dish as served
router.put(
  "/admin/orders/:orderId/items/:itemIndex/served",
  authenticateToken,
  markDishAsServed
);

// Update order items (admin)
router.put(
  "/admin/orders/:orderId/update-items",
  authenticateToken,
  updateAdminOrderItems
);

//user requests
router.get("/admin/user-requests", authenticateToken, getAllUserRequests);

router.get("/admin/analytics", authenticateToken, getDashboardAnalytics);
router.get("/admin/customers", authenticateToken, getCustomers);
router.get("/admin/customers/:id", authenticateToken, getCustomerDetails);
router.put("/admin/customers/:id", authenticateToken, updateCustomerDetails);
router.put(
  "/admin/customers/:id/status",
  authenticateToken,
  updateCustomerStatus
);
router.post("/admin/customers/create", authenticateToken, createCustomer);
router.post(
  "/admin/customers/:id/reset-password",
  authenticateToken,
  resetCustomerPassword
);

// Employee Management
router.get("/admin/employees", authenticateToken, getAllEmployees);
router.post("/admin/employees", authenticateToken, createEmployee);
router.put("/admin/employees/:id", authenticateToken, updateEmployee);
router.delete("/admin/employees/:id", authenticateToken, deleteEmployee);
router.post(
  "/admin/employees/:id/reset-password",
  authenticateToken,
  resetEmployeePassword
);

// Inventory Management
router.get("/admin/inventory", authenticateToken, getAllInventoryItems);
router.get("/admin/inventory/summary", authenticateToken, getInventorySummary);
router.get(
  "/admin/inventory/categories",
  authenticateToken,
  getInventoryCategories
);
router.get("/admin/inventory/low-stock", authenticateToken, getLowStockItems);
router.get("/admin/inventory/:id", authenticateToken, getInventoryItem);
router.post("/admin/inventory", authenticateToken, createInventoryItem);
router.put("/admin/inventory/:id", authenticateToken, updateInventoryItem);
router.delete("/admin/inventory/:id", authenticateToken, deleteInventoryItem);
router.post(
  "/admin/inventory/:id/quantity",
  authenticateToken,
  updateInventoryQuantity
);
router.get(
  "/admin/inventory-transactions",
  authenticateToken,
  getInventoryTransactions
);
router.post(
  "/admin/dishes/:dishId/ingredients",
  authenticateToken,
  linkInventoryToDish
);

// Admin profile routes
router.get("/admin/profile", authenticateToken, getAdminProfile);
router.put("/admin/profile", authenticateToken, updateAdminProfile);
router.put("/admin/change-password", authenticateToken, changeAdminPassword);

// Admin management routes
router.get("/admin/admins", authenticateToken, getAllAdmins);

// Vector embedding routes
router.post(
  "/admin/vector/generate-all",
  authenticateToken,
  generateEmbeddings
);
router.post(
  "/admin/vector/dishes/:dishId",
  authenticateToken,
  generateDishEmbedding
);
router.get("/admin/vector/similar-dishes", authenticateToken, getSimilarDishes);
router.get("/admin/vector/stats", authenticateToken, getEmbeddingStats);

export default router;
