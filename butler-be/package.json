{"name": "butler-be", "version": "1.0.0", "description": "backend for butler", "main": "index.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js", "dev": "nodemon index.js", "migrate-to-groq": "node scripts/migrate-to-groq.js", "test-ai": "node scripts/migrate-to-groq.js"}, "author": "v<PERSON><PERSON>han", "license": "ISC", "dependencies": {"@xenova/transformers": "^2.17.2", "bcrypt": "^5.1.0", "body-parser": "^1.20.3", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "groq-sdk": "^0.21.0", "html-pdf": "^3.0.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.2.0", "node-cron": "^3.0.3", "nodemailer": "^6.10.1", "nodemon": "^3.1.9", "ollama": "^0.5.14", "razorpay": "^2.9.6", "socket.io": "^4.8.1"}}