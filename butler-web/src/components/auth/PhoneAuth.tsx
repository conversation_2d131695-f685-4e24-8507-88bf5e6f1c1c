"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";
import { Loader2, Phone, User } from "lucide-react";

interface PhoneAuthProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

export default function PhoneAuth({ onSuccess, onCancel }: PhoneAuthProps) {
  const { loginWithPhone, registerWithPhone } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("login");

  // Login form state
  const [loginPhone, setLoginPhone] = useState("");

  // Registration form state
  const [registerPhone, setRegisterPhone] = useState("");
  const [registerName, setRegisterName] = useState("");
  const [registerAddress, setRegisterAddress] = useState("");

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!loginPhone.trim()) {
      toast.error("Please enter your phone number");
      return;
    }

    setIsLoading(true);
    try {
      const result = await loginWithPhone(loginPhone.trim());
      
      if (result.success) {
        toast.success("Login successful!");
        onSuccess?.();
      } else {
        toast.error("Login failed. Please check your phone number or register first.");
      }
    } catch (error) {
      console.error("Login error:", error);
      toast.error("Login failed. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!registerPhone.trim() || !registerName.trim()) {
      toast.error("Please enter your phone number and name");
      return;
    }

    setIsLoading(true);
    try {
      const result = await registerWithPhone(
        registerPhone.trim(),
        registerName.trim(),
        registerAddress.trim() || undefined
      );
      
      if (result.success) {
        toast.success("Registration successful! You are now logged in.");
        onSuccess?.();
      } else {
        toast.error("Registration failed. This phone number might already be registered.");
      }
    } catch (error) {
      console.error("Registration error:", error);
      toast.error("Registration failed. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const formatPhoneNumber = (value: string) => {
    // Remove all non-digits
    const digits = value.replace(/\D/g, "");
    
    // Add country code if not present
    if (digits.length > 0 && !digits.startsWith("91")) {
      return "+91" + digits;
    } else if (digits.startsWith("91")) {
      return "+" + digits;
    }
    
    return value;
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <CardTitle className="flex items-center justify-center gap-2">
          <Phone className="h-5 w-5" />
          Phone Authentication
        </CardTitle>
        <CardDescription>
          Login or register using your phone number
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="login">Login</TabsTrigger>
            <TabsTrigger value="register">Register</TabsTrigger>
          </TabsList>
          
          <TabsContent value="login" className="space-y-4">
            <form onSubmit={handleLogin} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="login-phone">Phone Number</Label>
                <Input
                  id="login-phone"
                  type="tel"
                  placeholder="+91 9876543210"
                  value={loginPhone}
                  onChange={(e) => setLoginPhone(formatPhoneNumber(e.target.value))}
                  disabled={isLoading}
                  required
                />
              </div>
              
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Logging in...
                  </>
                ) : (
                  "Login"
                )}
              </Button>
            </form>
          </TabsContent>
          
          <TabsContent value="register" className="space-y-4">
            <form onSubmit={handleRegister} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="register-phone">Phone Number</Label>
                <Input
                  id="register-phone"
                  type="tel"
                  placeholder="+91 9876543210"
                  value={registerPhone}
                  onChange={(e) => setRegisterPhone(formatPhoneNumber(e.target.value))}
                  disabled={isLoading}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="register-name">Full Name</Label>
                <Input
                  id="register-name"
                  type="text"
                  placeholder="Enter your full name"
                  value={registerName}
                  onChange={(e) => setRegisterName(e.target.value)}
                  disabled={isLoading}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="register-address">Address (Optional)</Label>
                <Input
                  id="register-address"
                  type="text"
                  placeholder="Enter your address"
                  value={registerAddress}
                  onChange={(e) => setRegisterAddress(e.target.value)}
                  disabled={isLoading}
                />
              </div>
              
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Registering...
                  </>
                ) : (
                  <>
                    <User className="mr-2 h-4 w-4" />
                    Register
                  </>
                )}
              </Button>
            </form>
          </TabsContent>
        </Tabs>
        
        {onCancel && (
          <div className="mt-4">
            <Button variant="outline" onClick={onCancel} className="w-full">
              Cancel
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
