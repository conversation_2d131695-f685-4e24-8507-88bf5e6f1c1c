/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useTheme } from "@/contexts/ThemeContext";
import { Icon } from "@iconify/react/dist/iconify.js";
import { useEffect, useState, useRef } from "react";

const MicRecorder = ({
  setUserMessage,
}: {
  setUserMessage: (message: string) => void;
}) => {
  const { theme } = useTheme();
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState("");
  const recognitionRef = useRef<any | null>(null);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const SpeechRecognition =
        (window as any).webkitSpeechRecognition ||
        (window as any).SpeechRecognition;

      if (!SpeechRecognition) {
        alert("Speech Recognition API not supported in this browser.");
        return;
      }

      const recognition = new SpeechRecognition();
      recognition.lang = "en-US";
      recognition.interimResults = false;
      recognition.continuous = false;

      recognition.onresult = (event: any) => {
        const result = event.results[0][0].transcript;
        setTranscript(result);
      };

      recognition.onerror = (event: any) => {
        console.error("Speech recognition error:", event.error);
        setIsListening(false);
      };

      recognitionRef.current = recognition;
    }
  }, []);

  useEffect(() => {
    if (transcript) {
      setUserMessage(transcript);
    }
  }, [transcript]);

  const toggleListening = () => {
    if (!recognitionRef.current) return;

    if (isListening) {
      recognitionRef.current.stop();
      setIsListening(false);
    } else {
      setTranscript("");
      recognitionRef.current.start();
      setIsListening(true);
    }
  };

  return (
    <button onClick={toggleListening}>
      {isListening ? (
        <div className="flex items-center gap-2">
          <div className="w-9 h-9 rounded-full bg-gray-200 animate-pulse relative flex items-center justify-center">
            <div className="w-6 h-6 bg-gray-400 rounded-full animate-pulse absolute flex items-center justify-center">
              <div className="w-3 h-3 bg-gray-500 rounded-full animate-pulse absolute" />
            </div>
          </div>
          <Icon
            icon="carbon:stop-filled"
            color={theme.primaryColor}
            width="32"
            height="32"
          />
        </div>
      ) : (
        <Icon
          icon="fluent:mic-20-regular"
          color={theme.primaryColor}
          width="32"
          height="32"
        />
      )}
    </button>
  );
};

export default MicRecorder;
