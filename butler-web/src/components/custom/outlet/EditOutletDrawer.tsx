"use client";
import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  She<PERSON><PERSON><PERSON><PERSON>,
  SheetDes<PERSON>,
} from "@/components/ui/sheet";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { updateOutlet } from "@/server/admin";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "sonner";
import { Outlet, OperatingHours, PaymentSettings } from "@/app/type";
import { useTheme } from "@/contexts/ThemeContext";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { Loader2 } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface EditOutletDrawerProps {
  outlet: Outlet;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

const defaultOperatingHours: OperatingHours[] = [
  { day: "monday", isOpen: true, openTime: "09:00", closeTime: "22:00" },
  { day: "tuesday", isOpen: true, openTime: "09:00", closeTime: "22:00" },
  { day: "wednesday", isOpen: true, openTime: "09:00", closeTime: "22:00" },
  { day: "thursday", isOpen: true, openTime: "09:00", closeTime: "22:00" },
  { day: "friday", isOpen: true, openTime: "09:00", closeTime: "22:00" },
  { day: "saturday", isOpen: true, openTime: "09:00", closeTime: "22:00" },
  { day: "sunday", isOpen: true, openTime: "09:00", closeTime: "22:00" },
];

const defaultPaymentSettings: PaymentSettings = {
  acceptCash: true,
  acceptOnline: true,
  defaultPaymentMethod: "cash",
};

const EditOutletDrawer: React.FC<EditOutletDrawerProps> = ({
  outlet,
  open,
  onOpenChange,
  onSuccess,
}) => {
  const { theme } = useTheme();
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("basic");

  const [updatedOutlet, setUpdatedOutlet] = useState<Outlet>({
    ...outlet,
    operatingHours: outlet.operatingHours || defaultOperatingHours,
    paymentSettings: outlet.paymentSettings || defaultPaymentSettings,
    deliveryRadius: outlet.deliveryRadius || 5,
    deliveryZones: outlet.deliveryZones || [],
    status: outlet.status || "active",
  });

  useEffect(() => {
    if (open) {
      setUpdatedOutlet({
        ...outlet,
        operatingHours: outlet.operatingHours || defaultOperatingHours,
        paymentSettings: outlet.paymentSettings || defaultPaymentSettings,
        deliveryRadius: outlet.deliveryRadius || 5,
        deliveryZones: outlet.deliveryZones || [],
        status: outlet.status || "active",
      });
    }
  }, [open, outlet]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setUpdatedOutlet((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleCheckboxChange = (checked: boolean) => {
    setUpdatedOutlet((prev) => ({
      ...prev,
      isCloudKitchen: checked,
    }));
  };

  const handleStatusChange = (checked: boolean) => {
    setUpdatedOutlet((prev) => ({
      ...prev,
      status: checked ? "active" : "inactive",
    }));
  };

  const handleOperatingHoursChange = (
    day: string,
    field: keyof OperatingHours,
    value: string | boolean | number
  ) => {
    setUpdatedOutlet((prev) => ({
      ...prev,
      operatingHours: prev.operatingHours?.map((hours) =>
        hours.day === day ? { ...hours, [field]: value } : hours
      ),
    }));
  };

  const handlePaymentSettingsChange = (
    field: keyof PaymentSettings,
    value: string | boolean | number
  ) => {
    setUpdatedOutlet((prev) => ({
      ...prev,
      paymentSettings: {
        ...prev.paymentSettings!,
        [field]: value,
      },
    }));
  };

  const handleDeliveryRadiusChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = parseInt(e.target.value);
    if (!isNaN(value) && value > 0) {
      setUpdatedOutlet((prev) => ({
        ...prev,
        deliveryRadius: value,
      }));
    }
  };

  const validateForm = () => {
    if (!updatedOutlet.name.trim()) {
      toast.error("Outlet name is required");
      return false;
    }
    if (!updatedOutlet.address.trim()) {
      toast.error("Address is required");
      return false;
    }
    if (!updatedOutlet.contact.trim()) {
      toast.error("Contact number is required");
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);
      const response = await updateOutlet(updatedOutlet);

      if (response.success) {
        toast.success("Outlet updated successfully");
        onOpenChange(false);
        onSuccess();
      } else {
        toast.error(response.message || "Failed to update outlet");
      }
    } catch (error) {
      console.error(error);
      toast.error("An unexpected error occurred");
    } finally {
      setLoading(false);
    }
  };

  const formatDay = (day: string) => {
    return day.charAt(0).toUpperCase() + day.slice(1);
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent
        className="sm:max-w-[500px] overflow-y-auto"
        style={{ color: theme.primaryColor }}
      >
        <SheetHeader>
          <SheetTitle>Edit Outlet</SheetTitle>
          <SheetDescription>
            Update outlet details and settings
          </SheetDescription>
        </SheetHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="basic">Basic Info</TabsTrigger>
            {/* <TabsTrigger value="hours">Operating Hours</TabsTrigger> */}
            {/* <TabsTrigger value="delivery">Delivery & Payment</TabsTrigger> */}
          </TabsList>

          <TabsContent value="basic" className="space-y-4 py-4 p-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="status" className="text-base">
                  Outlet Status
                </Label>
                <p className="text-sm text-gray-500">
                  Enable or disable this outlet
                </p>
              </div>
              <Switch
                id="status"
                checked={updatedOutlet.status === "active"}
                onCheckedChange={handleStatusChange}
              />
            </div>

            <Separator className="my-4" />

            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Outlet Name</Label>
                <Input
                  id="name"
                  name="name"
                  value={updatedOutlet.name}
                  onChange={handleInputChange}
                  placeholder="Enter outlet name"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="address">Address</Label>
                <Input
                  id="address"
                  name="address"
                  value={updatedOutlet.address}
                  onChange={handleInputChange}
                  placeholder="Enter outlet address"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="contact">Contact Number</Label>
                <Input
                  id="contact"
                  name="contact"
                  value={updatedOutlet.contact}
                  onChange={handleInputChange}
                  placeholder="Enter contact number"
                />
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isCloudKitchen"
                  checked={updatedOutlet.isCloudKitchen}
                  onCheckedChange={handleCheckboxChange}
                />
                <Label
                  htmlFor="isCloudKitchen"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Is this a cloud kitchen?
                </Label>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="hours" className="space-y-4 py-4">
            <div className="space-y-4">
              {updatedOutlet.operatingHours?.map((hours) => (
                <div key={hours.day} className="border p-4 rounded-md">
                  <div className="flex items-center justify-between mb-4">
                    <Label className="font-medium">
                      {formatDay(hours.day)}
                    </Label>
                    <Switch
                      checked={hours.isOpen}
                      onCheckedChange={(checked) =>
                        handleOperatingHoursChange(hours.day, "isOpen", checked)
                      }
                    />
                  </div>

                  {hours.isOpen && (
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor={`${hours.day}-open`}>
                          Opening Time
                        </Label>
                        <Input
                          id={`${hours.day}-open`}
                          type="time"
                          value={hours.openTime}
                          onChange={(e) =>
                            handleOperatingHoursChange(
                              hours.day,
                              "openTime",
                              e.target.value
                            )
                          }
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor={`${hours.day}-close`}>
                          Closing Time
                        </Label>
                        <Input
                          id={`${hours.day}-close`}
                          type="time"
                          value={hours.closeTime}
                          onChange={(e) =>
                            handleOperatingHoursChange(
                              hours.day,
                              "closeTime",
                              e.target.value
                            )
                          }
                        />
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="delivery" className="space-y-4 py-4">
            <div className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Delivery Settings</h3>
                <div className="grid gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="deliveryRadius">Delivery Radius (km)</Label>
                    <Input
                      id="deliveryRadius"
                      type="number"
                      min="1"
                      value={updatedOutlet.deliveryRadius}
                      onChange={handleDeliveryRadiusChange}
                    />
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Payment Settings</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="acceptCash">Accept Cash Payments</Label>
                    <Switch
                      id="acceptCash"
                      checked={updatedOutlet.paymentSettings?.acceptCash}
                      onCheckedChange={(checked) =>
                        handlePaymentSettingsChange("acceptCash", checked)
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="acceptOnline">Accept Online Payments</Label>
                    <Switch
                      id="acceptOnline"
                      checked={updatedOutlet.paymentSettings?.acceptOnline}
                      onCheckedChange={(checked) =>
                        handlePaymentSettingsChange("acceptOnline", checked)
                      }
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="defaultPaymentMethod">
                      Default Payment Method
                    </Label>
                    <Select
                      value={
                        updatedOutlet.paymentSettings?.defaultPaymentMethod
                      }
                      onValueChange={(value) =>
                        handlePaymentSettingsChange(
                          "defaultPaymentMethod",
                          value as "cash" | "online"
                        )
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select default payment method" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="cash">Cash</SelectItem>
                        <SelectItem value="online">Online</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <SheetFooter className="mt-4">
          <Button
            type="submit"
            onClick={handleSubmit}
            disabled={loading}
            style={{ backgroundColor: theme.primaryColor }}
          >
            {loading ? (
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Updating...</span>
              </div>
            ) : (
              "Update Outlet"
            )}
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
};

export default EditOutletDrawer;
