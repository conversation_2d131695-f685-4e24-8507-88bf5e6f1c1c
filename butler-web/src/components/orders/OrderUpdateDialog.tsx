"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import {
  Loader2,
  Plus,
  Minus,
  ShoppingCart,
  Edit3,
  Tag,
  X,
} from "lucide-react";
import { updateOrderItems } from "@/server/user";
import { getOutletMenu } from "@/server/user";

interface OrderItem {
  dishId: {
    _id: string;
    name: string;
    price: number;
  };
  quantity: number;
  price: number;
  isServed?: boolean;
  servedQuantity?: number;
}

interface Order {
  _id: string;
  items: OrderItem[];
  totalAmount: number;
  finalAmount: number;
  paymentStatus: string;
  status: string;
  couponCode?: string;
  couponDiscount?: number;
}

interface Dish {
  _id: string;
  name: string;
  price: number;
  category: string;
  description?: string;
  image?: string;
  isAvailable: boolean;
}

interface OrderUpdateDialogProps {
  order: Order;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export default function OrderUpdateDialog({
  order,
  isOpen,
  onClose,
  onSuccess,
}: OrderUpdateDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [dishes, setDishes] = useState<Dish[]>([]);
  const [updatedItems, setUpdatedItems] = useState<OrderItem[]>([]);
  const [couponCode, setCouponCode] = useState("");
  const [appliedCoupon, setAppliedCoupon] = useState<{
    code: string;
    discount: number;
    discountType: string;
    discountValue: number;
  } | null>(null);
  const [couponLoading, setCouponLoading] = useState(false);

  // Automatically recalculate coupon when items change
  useEffect(() => {
    if (appliedCoupon && updatedItems.length > 0) {
      const subtotal = updatedItems.reduce(
        (sum, item) => sum + item.price * item.quantity,
        0
      );

      // Only recalculate if the subtotal has changed
      const currentSubtotal = order.items.reduce(
        (sum, item) => sum + item.price * item.quantity,
        0
      );

      if (subtotal !== currentSubtotal) {
        // Automatically revalidate and recalculate the coupon
        revalidateCoupon(appliedCoupon.code, subtotal);
      }
    }
  }, [updatedItems, appliedCoupon, order.items]);

  const revalidateCoupon = async (couponCode: string, amount: number) => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/user/coupons/validate`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("user-token")}`,
          },
          body: JSON.stringify({
            code: couponCode,
            outletId: localStorage.getItem("outletId") || "",
            amount: amount,
          }),
        }
      );

      const result = await response.json();

      if (result.success) {
        setAppliedCoupon({
          code: couponCode.toUpperCase(),
          discount: result.data.discount,
          discountType: result.data.coupon.discountType,
          discountValue: result.data.coupon.discountValue,
        });
      } else {
        // Coupon is no longer valid, remove it
        setAppliedCoupon(null);
        toast.error(`Coupon removed: ${result.message}`);
      }
    } catch (error) {
      console.error("Error revalidating coupon:", error);
      // Remove coupon on error to be safe
      setAppliedCoupon(null);
      toast.error("Coupon removed due to validation error");
    }
  };

  useEffect(() => {
    if (isOpen) {
      setUpdatedItems([...order.items]);
      setCouponCode("");
      setAppliedCoupon(
        order.couponCode && order.couponDiscount
          ? {
              code: order.couponCode,
              discount: order.couponDiscount,
              discountType: "fixed", // We'll get this from validation
              discountValue: order.couponDiscount,
            }
          : null
      );
      fetchDishes();
    }
  }, [isOpen, order]);

  const fetchDishes = async () => {
    const outletId = localStorage.getItem("outletId") || "";
    const foodChainId = localStorage.getItem("chainId") || "";
    try {
      const response = await getOutletMenu(foodChainId, outletId);
      console.log(response, "the dishes");
      if (response.success) {
        setDishes(response.data.filter((dish: Dish) => dish.isAvailable));
      }
    } catch (error) {
      console.error("Error fetching dishes:", error);
      toast.error("Failed to load dishes");
    }
  };

  const addDishToOrder = (dish: Dish) => {
    const existingItemIndex = updatedItems.findIndex(
      (item) => item.dishId._id === dish._id
    );

    if (existingItemIndex >= 0) {
      const newItems = [...updatedItems];
      newItems[existingItemIndex].quantity += 1;
      setUpdatedItems(newItems);
    } else {
      const newItem: OrderItem = {
        dishId: {
          _id: dish._id,
          name: dish.name,
          price: dish.price,
        },
        quantity: 1,
        price: dish.price,
        isServed: false,
      };
      setUpdatedItems([...updatedItems, newItem]);
    }
  };

  const updateItemQuantity = (itemIndex: number, newQuantity: number) => {
    const item = updatedItems[itemIndex];
    const servedQuantity = item.servedQuantity || 0;

    // If dish has served quantities, don't allow reducing quantity below served amount
    if (servedQuantity > 0 && newQuantity < servedQuantity) {
      toast.error(
        `Cannot reduce quantity below served amount (${servedQuantity}). You can only increase the quantity.`
      );
      return;
    }

    if (newQuantity <= 0) {
      // Don't allow removing dishes with served quantities
      if (servedQuantity > 0) {
        toast.error(
          "Cannot remove dishes that have been partially or fully served."
        );
        return;
      }
      const newItems = updatedItems.filter((_, index) => index !== itemIndex);
      setUpdatedItems(newItems);
    } else {
      const newItems = [...updatedItems];
      newItems[itemIndex].quantity = newQuantity;
      setUpdatedItems(newItems);
    }
  };

  const handleApplyCoupon = async () => {
    if (!couponCode.trim()) {
      toast.error("Please enter a coupon code");
      return;
    }

    const subtotal = updatedItems.reduce(
      (sum, item) => sum + item.price * item.quantity,
      0
    );

    setCouponLoading(true);
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/user/coupons/validate`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("user-token")}`,
          },
          body: JSON.stringify({
            code: couponCode,
            outletId: localStorage.getItem("outletId") || "",
            amount: subtotal,
          }),
        }
      );

      const result = await response.json();

      if (result.success) {
        setAppliedCoupon({
          code: couponCode.toUpperCase(),
          discount: result.data.discount,
          discountType: result.data.coupon.discountType,
          discountValue: result.data.coupon.discountValue,
        });
        toast.success("Coupon applied successfully!");
        setCouponCode("");
      } else {
        toast.error(result.message || "Failed to apply coupon");
      }
    } catch (error) {
      console.error("Error applying coupon:", error);
      toast.error("Failed to apply coupon");
    } finally {
      setCouponLoading(false);
    }
  };

  const removeCoupon = () => {
    setAppliedCoupon(null);
    toast.success("Coupon removed");
  };

  const calculateTotal = () => {
    const total = updatedItems.reduce(
      (sum, item) => sum + item.price * item.quantity,
      0
    );
    const discount = appliedCoupon ? appliedCoupon.discount : 0;
    return Math.max(0, total - discount);
  };

  const handleUpdateOrder = async () => {
    if (updatedItems.length === 0) {
      toast.error("Please add at least one item to the order");
      return;
    }

    setIsLoading(true);
    try {
      const itemsToUpdate = updatedItems.map((item) => ({
        dishId: item.dishId._id,
        quantity: item.quantity,
        price: item.price,
      }));

      const response = await updateOrderItems(
        order._id,
        itemsToUpdate,
        appliedCoupon?.code || undefined,
        undefined // Let backend recalculate the discount
      );

      if (response.success) {
        toast.success("Order updated successfully!");
        onSuccess();
        onClose();
      } else {
        toast.error("Failed to update order");
      }
    } catch (error) {
      console.error("Error updating order:", error);
      toast.error("Failed to update order");
    } finally {
      setIsLoading(false);
    }
  };

  const availableDishes = dishes.filter(
    (dish) => !updatedItems.some((item) => item.dishId._id === dish._id)
  );

  console.log(dishes);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit3 className="h-5 w-5" />
            Update Order #{order._id.slice(-6)}
          </DialogTitle>
          <DialogDescription>
            Add or remove items from your order. Changes can only be made before
            payment.
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Add More Items</h3>
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {availableDishes.map((dish) => (
                <Card
                  key={dish._id}
                  className="cursor-pointer hover:shadow-md transition-shadow"
                >
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h4 className="font-medium">{dish.name}</h4>
                        <p className="text-sm text-muted-foreground">
                          {dish.description}
                        </p>
                        <p className="text-sm font-medium text-green-600">
                          ₹{dish.price}
                        </p>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => addDishToOrder(dish)}
                        disabled={isLoading}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
          {/* Current Order Items */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Current Order</h3>
            <div className="space-y-2">
              {updatedItems.map((item, index) => (
                <Card key={index}>
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h4 className="font-medium">{item.dishId.name}</h4>
                        <p className="text-sm text-muted-foreground">
                          ₹{item.price} each
                        </p>
                        {item.isServed && (
                          <Badge variant="secondary" className="mt-1">
                            Served
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            updateItemQuantity(index, item.quantity - 1)
                          }
                          disabled={
                            isLoading ||
                            Boolean(
                              item.servedQuantity && item.servedQuantity > 0
                            )
                          }
                          title={
                            item.servedQuantity && item.servedQuantity > 0
                              ? `Cannot reduce quantity below served amount (${item.servedQuantity})`
                              : "Decrease quantity"
                          }
                        >
                          <Minus className="h-4 w-4" />
                        </Button>
                        <span className="w-8 text-center">{item.quantity}</span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            updateItemQuantity(index, item.quantity + 1)
                          }
                          disabled={isLoading}
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="mt-2 text-right">
                      <span className="font-medium">
                        ₹{(item.price * item.quantity).toFixed(2)}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
          {/* Coupon Section */}

          {/* Available Dishes */}
        </div>
        <Card>
          <CardContent className="p-4">
            <div className="space-y-4">
              <Label htmlFor="coupon">Apply Coupon</Label>

              {!appliedCoupon ? (
                <div className="flex gap-2">
                  <Input
                    id="coupon"
                    value={couponCode}
                    onChange={(e) =>
                      setCouponCode(e.target.value.toUpperCase())
                    }
                    placeholder="Enter coupon code"
                    disabled={isLoading || couponLoading}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleApplyCoupon}
                    disabled={isLoading || couponLoading || !couponCode.trim()}
                  >
                    {couponLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <>
                        <Tag className="h-4 w-4 mr-1" />
                        Apply
                      </>
                    )}
                  </Button>
                </div>
              ) : (
                <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Tag className="h-4 w-4 text-green-600" />
                    <span className="font-medium text-green-800">
                      {appliedCoupon.code}
                    </span>
                    <span className="text-sm text-green-600">
                      (₹{appliedCoupon.discount} off)
                    </span>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={removeCoupon}
                    disabled={isLoading}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Order Summary */}
        <Card>
          <CardContent className="p-4">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Subtotal:</span>
                <span>
                  ₹
                  {updatedItems
                    .reduce((sum, item) => sum + item.price * item.quantity, 0)
                    .toFixed(2)}
                </span>
              </div>
              {appliedCoupon && appliedCoupon.discount > 0 && (
                <div className="flex justify-between text-green-600">
                  <span>Discount ({appliedCoupon.code}):</span>
                  <span>-₹{appliedCoupon.discount.toFixed(2)}</span>
                </div>
              )}
              <Separator />
              <div className="flex justify-between font-semibold">
                <span>Total:</span>
                <span>₹{calculateTotal().toFixed(2)}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end gap-2 pt-4">
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button
            onClick={handleUpdateOrder}
            disabled={isLoading || updatedItems.length === 0}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Updating...
              </>
            ) : (
              <>
                <ShoppingCart className="mr-2 h-4 w-4" />
                Update Order
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
