"use client";

import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "sonner";
import { CheckCircle, Clock, User, Calendar } from "lucide-react";
import { format } from "date-fns";

interface OrderItem {
  dishId: {
    _id: string;
    name: string;
    price: number;
  };
  quantity: number;
  price: number;
  isServed?: boolean;
  servedAt?: string;
  servedBy?: {
    _id: string;
    name: string;
  };
  servedQuantity?: number;
}

interface DishServingStatusProps {
  items: OrderItem[];
  isAdmin?: boolean;
  orderId?: string;
  onServingStatusChange?: (itemIndex: number, isServed: boolean) => void;
}

export default function DishServingStatus({
  items,
  isAdmin = false,
  orderId,
  onServingStatusChange,
}: DishServingStatusProps) {
  const [updatingItems, setUpdatingItems] = useState<Set<number>>(new Set());

  const handleServingToggle = async (
    itemIndex: number,
    currentStatus: boolean
  ) => {
    if (!isAdmin || !onServingStatusChange) return;

    const newStatus = !currentStatus;
    setUpdatingItems((prev) => new Set(prev).add(itemIndex));

    try {
      await onServingStatusChange(itemIndex, newStatus);
      toast.success(
        newStatus ? "Dish marked as served" : "Dish marked as not served"
      );
    } catch (error) {
      console.error("Error updating serving status:", error);
      toast.error("Failed to update serving status");
    } finally {
      setUpdatingItems((prev) => {
        const newSet = new Set(prev);
        newSet.delete(itemIndex);
        return newSet;
      });
    }
  };

  const getServingStatusBadge = (item: OrderItem) => {
    if (item.isServed) {
      return (
        <Badge
          variant="default"
          className="bg-green-100 text-green-800 border-green-200"
        >
          <CheckCircle className="w-3 h-3 mr-1" />
          Served
        </Badge>
      );
    } else {
      return (
        <Badge
          variant="secondary"
          className="bg-yellow-100 text-yellow-800 border-yellow-200"
        >
          <Clock className="w-3 h-3 mr-1" />
          Pending
        </Badge>
      );
    }
  };

  const allItemsServed = items.every((item) => item.isServed);
  const servedCount = items.filter((item) => item.isServed).length;

  return (
    <div className="space-y-4">
      {/* Overall Status */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold text-lg">Order Progress</h3>
              <p className="text-sm text-muted-foreground">
                {servedCount} of {items.length} dishes served
              </p>
            </div>
            <div className="flex items-center gap-2">
              {allItemsServed ? (
                <Badge
                  variant="default"
                  className="bg-green-100 text-green-800 border-green-200"
                >
                  <CheckCircle className="w-4 h-4 mr-1" />
                  Complete
                </Badge>
              ) : (
                <Badge
                  variant="secondary"
                  className="bg-blue-100 text-blue-800 border-blue-200"
                >
                  <Clock className="w-4 h-4 mr-1" />
                  In Progress
                </Badge>
              )}
            </div>
          </div>

          {/* Progress Bar */}
          <div className="mt-3">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-green-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${(servedCount / items.length) * 100}%` }}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Individual Dishes */}
      <div className="space-y-3">
        {items.map((item, index) => (
          <Card
            key={index}
            className={`transition-all duration-200 ${
              item.isServed ? "bg-green-50 border-green-200" : "bg-white"
            }`}
          >
            <CardContent className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3">
                    {isAdmin && (
                      <Checkbox
                        checked={item.isServed || false}
                        onCheckedChange={() =>
                          handleServingToggle(index, item.isServed || false)
                        }
                        disabled={updatingItems.has(index)}
                        className="mt-1"
                      />
                    )}
                    <div>
                      <h4 className="font-medium text-base">
                        {item.dishId.name}
                      </h4>
                      <div className="flex items-center gap-4 mt-1">
                        <span className="text-sm text-muted-foreground">
                          Quantity: {item.quantity}
                        </span>
                        <span>
                          {item.servedQuantity && item.servedQuantity > 0 && (
                            <span className="text-sm text-muted-foreground">
                              Served: {item.servedQuantity}
                            </span>
                          )}
                        </span>
                        <span className="text-sm text-muted-foreground">
                          ₹{item.price} each
                        </span>
                        <span className="text-sm font-medium">
                          Total: ₹{(item.price * item.quantity).toFixed(2)}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Serving Details */}
                  {item.isServed && item.servedAt && (
                    <div className="mt-3 p-3 bg-green-50 rounded-lg border border-green-200">
                      <div className="flex items-center gap-4 text-sm text-green-700">
                        <div className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          <span>
                            Served on{" "}
                            {format(
                              new Date(item.servedAt),
                              "MMM dd, yyyy 'at' hh:mm a"
                            )}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="flex flex-col items-end gap-2">
                  {getServingStatusBadge(item)}

                  {isAdmin && !item.isServed && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        handleServingToggle(index, item.isServed || false)
                      }
                      disabled={updatingItems.has(index)}
                      className="text-xs"
                    >
                      Mark as Served
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Summary for Admin */}
      {isAdmin && (
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-blue-900">Kitchen Status</h4>
                <p className="text-sm text-blue-700">
                  {allItemsServed
                    ? "All dishes have been served. Order is complete!"
                    : `${
                        items.length - servedCount
                      } dishes still need to be served.`}
                </p>
              </div>
              {!allItemsServed && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    items.forEach((item, index) => {
                      if (!item.isServed) {
                        handleServingToggle(index, false);
                      }
                    });
                  }}
                  className="text-blue-700 border-blue-300 hover:bg-blue-100"
                >
                  Mark All as Served
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
