/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import { useEffect, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  <PERSON>Chart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
} from "recharts";
import { getAnalytics } from "@/server/admin";
import { Indian } from "@/lib/currency";
import { TrendingUp, TrendingDown } from "lucide-react";
import { formatDateWithTimestamp } from "@/app/helper/date";
import DateRangeSlider from "@/components/custom/analytics/DateRangeSelector";
const GrowthIndicator = ({ value }: { value: number }) => {
  const isPositive = value >= 0;
  return (
    <div
      className={`flex items-center ${
        isPositive ? "text-green-500" : "text-red-500"
      }`}
    >
      {isPositive ? (
        <TrendingUp className="h-4 w-4" />
      ) : (
        <TrendingDown className="h-4 w-4" />
      )}
      <span className="ml-1">{Math.abs(value).toFixed(1)}%</span>
    </div>
  );
};

const AnalyticsDashboard = () => {
  const [analytics, setAnalytics] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [daysToAnalyze, setDaysToAnalyze] = useState(30);
  const [foodChainData, setFoodChainData] = useState<any>(null);
  const [formattedData, setFormattedData] = useState<any>(null);
  useEffect(() => {
    const fetchAnalytics = async () => {
      const response = await getAnalytics(daysToAnalyze);
      if (response.success) {
        setAnalytics(response.data);
        setFoodChainData(response.data.foodChainData);
        const data = response.data.peakHours.map((item: any) => ({
          ...item,
          formattedHour: formatHour(item._id),
        }));
        setFormattedData(data);
      }
      setLoading(false);
    };

    fetchAnalytics();
  }, [daysToAnalyze]);

  const formatHour = (hour: number) => {
    if (hour === 0) return "12 AM";
    if (hour === 12) return "12 PM";
    return hour < 12 ? `${hour} AM` : `${hour - 12} PM`;
  };

  // Format data for display

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Analytics Dashboard</h2>
        <div className="text-sm text-muted-foreground">
          {daysToAnalyze} days
        </div>
      </div>
      <div className="text-sm text-muted-foreground">
        <div className="flex items-center gap-2">
          <DateRangeSlider
            creationDate={formatDateWithTimestamp(foodChainData.createdAt)}
            onRangeChange={(days) => setDaysToAnalyze(days)}
          />
        </div>
      </div>
      <Separator />

      {/* Enhanced Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="text-sm text-muted-foreground">Total Revenue</div>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">
                {Indian(analytics?.summary?.totalRevenue || 0)}
              </div>
              <GrowthIndicator
                value={analytics?.summary?.growth?.revenue || 0}
              />
            </div>
            <div className="text-xs text-muted-foreground">
              vs. previous 30 days
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="text-sm text-muted-foreground">Total Orders</div>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">
                {analytics?.summary?.totalOrders || 0}
              </div>
              <GrowthIndicator
                value={analytics?.summary?.growth?.orders || 0}
              />
            </div>
            <div className="text-xs text-muted-foreground">
              vs. previous 30 days
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="text-sm text-muted-foreground">
              Average Order Value
            </div>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">
                {Indian(analytics?.summary?.averageOrderValue || 0)}
              </div>
              <GrowthIndicator
                value={analytics?.summary?.growth?.averageOrderValue || 0}
              />
            </div>
            <div className="text-xs text-muted-foreground">
              vs. previous 30 days
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="text-sm text-muted-foreground">
              Unique Customers
            </div>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">
                {analytics?.summary?.uniqueCustomers || 0}
              </div>
              <GrowthIndicator
                value={analytics?.summary?.growth?.uniqueCustomers || 0}
              />
            </div>
            <div className="text-xs text-muted-foreground">
              vs. previous 30 days
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Revenue Chart */}
      <Card>
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold mb-4">Revenue Trend</h3>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={analytics?.dailyRevenue}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="_id"
                  tickFormatter={(value) => {
                    return new Date(value).toLocaleDateString("en-US", {
                      month: "short",
                      day: "numeric",
                    });
                  }}
                />
                <YAxis tickFormatter={(value) => Indian(value)} />
                <Tooltip
                  formatter={(value: number) => [Indian(value), "Revenue"]}
                  labelFormatter={(label) =>
                    new Date(label).toLocaleDateString("en-US", {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    })
                  }
                />
                <Line
                  type="monotone"
                  dataKey="revenue"
                  stroke="#8884d8"
                  strokeWidth={2}
                  dot={false}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Top Selling Items */}
        <Card>
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold mb-4">Top Selling Items</h3>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={analytics?.topItems} layout="vertical">
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" />
                  <YAxis
                    dataKey="dishDetails[0].name"
                    type="category"
                    width={150}
                    tick={{ fontSize: 12 }}
                  />
                  <Tooltip
                    formatter={(value: number) => [
                      `${value} orders`,
                      "Quantity Sold",
                    ]}
                  />
                  <Bar
                    dataKey="totalQuantity"
                    fill="#8884d8"
                    name="Quantity Sold"
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Outlet Performance */}
        <Card>
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold mb-4">Outlet Performance</h3>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={analytics?.outletPerformance} layout="vertical">
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" />
                  <YAxis
                    dataKey="outletDetails[0].name"
                    type="category"
                    width={150}
                    tick={{ fontSize: 12 }}
                  />
                  <Tooltip
                    formatter={(value: number) => [Indian(value), "Revenue"]}
                  />
                  <Bar dataKey="totalRevenue" fill="#82ca9d" name="Revenue" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Peak Hours Analysis */}
      <Card>
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold mb-4">Peak Hours</h3>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={formattedData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="formattedHour"
                  label={{ value: "Hour of Day", position: "bottom" }}
                />
                <YAxis />
                <Tooltip
                  formatter={(value, name) => [
                    name === "orderCount"
                      ? value + " orders"
                      : Indian(Number(value)),
                    name === "orderCount" ? "Orders" : "Revenue",
                  ]}
                />
                <Bar dataKey="orderCount" fill="#8884d8" name="Orders" />
                <Bar dataKey="revenue" fill="#82ca9d" name="Revenue" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Customer Retention */}
      <Card>
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold mb-4">
            Customer Order Frequency
          </h3>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={analytics?.customerRetention}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="_id"
                  label={{ value: "Number of Orders", position: "bottom" }}
                />
                <YAxis
                  label={{
                    value: "Number of Customers",
                    angle: -90,
                    position: "left",
                  }}
                />
                <Tooltip />
                <Bar dataKey="orderCount" fill="#8884d8" name="Customers" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Popular Combinations */}
      <Card>
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold mb-4">Popular Combinations</h3>
          <div className="space-y-4">
            {analytics?.popularCombinations.map((combo: any, index: number) => (
              <div
                key={index}
                className="flex items-center justify-between p-3 bg-muted rounded-lg"
              >
                <div className="flex-1">
                  <div className="font-medium">
                    {combo.dish1Details[0]?.name}
                  </div>
                  <div className="font-medium">
                    {combo.dish2Details[0]?.name}
                  </div>
                </div>
                <div className="text-sm text-muted-foreground">
                  Ordered together {combo.count} times
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AnalyticsDashboard;
