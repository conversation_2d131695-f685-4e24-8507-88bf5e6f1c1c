"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { getRoutes } from "@/routes";
import { ArrowLeft, ChevronLeft } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React from "react";
import { UserButton } from "@/components/UserButton";
import { Toaster } from "sonner";
import ResponsiveSidebar from "@/components/layouts/ResponsiveSidebar";

const Layout = ({ children }: { children: React.ReactNode }) => {
  const router = useRouter();
  const routes = getRoutes("user");

  // Add icons to user routes for consistency
  const routesWithIcons = routes.map((route) => ({
    ...route,
    icon: route.icon || "material-symbols:settings-outline", // Default icon if none provided
  }));

  return (
    <div className="min-h-screen flex flex-col sm:flex-row">
      {/* User settings sidebar with custom content */}
      <ResponsiveSidebar
        routes={routesWithIcons}
        title="Settings"
        backgroundColor="#1f2937" // gray-800
        textColor="#ffffff"
      >
        {/* Back to conversations button - only visible on desktop */}
        <div className="hidden sm:block mt-4">
          <Link href="/conversations">
            <Button variant="outline" size="sm" className="w-full">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Conversations
            </Button>
          </Link>
        </div>
      </ResponsiveSidebar>

      {/* Main content */}
      <div className="flex-1 flex flex-col min-h-screen overflow-y-auto">
        <header className="p-4 flex justify-between items-center border-b">
          <Button onClick={() => router.back()} variant="outline" size="sm">
            <ChevronLeft className="mr-1 h-4 w-4" /> Back
          </Button>
          <UserButton />
        </header>
        <div className="p-4 flex-1">{children}</div>
      </div>
      <Toaster />
    </div>
  );
};

export default Layout;
