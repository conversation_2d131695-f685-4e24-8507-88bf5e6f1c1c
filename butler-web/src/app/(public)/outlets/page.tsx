"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { MapPin, Phone, Search, Star, Truck } from "lucide-react";
import { toast } from "sonner";
import { Outlet } from "@/app/type";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface OutletWithDistance extends Omit<Outlet, "foodChain"> {
  distance?: number;
  foodChain: {
    name: string;
    theme: {
      primaryColor: string;
      name: string;
    };
  };
}

interface PaginationInfo {
  total: number;
  page: number;
  limit: number;
  pages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

const OutletsPage = () => {
  const [outlets, setOutlets] = useState<OutletWithDistance[]>([]);
  const [recommendations, setRecommendations] = useState<OutletWithDistance[]>(
    []
  );
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState<PaginationInfo>({
    total: 0,
    page: 1,
    limit: 20,
    pages: 0,
    hasNext: false,
    hasPrev: false,
  });

  // Filters
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCity, setSelectedCity] = useState("");
  const [selectedPincode, setSelectedPincode] = useState("");
  const [availableCities, setAvailableCities] = useState<string[]>([]);

  // Fetch outlets with filters
  const fetchOutlets = useCallback(
    async (page = 1) => {
      try {
        setLoading(true);
        const params = new URLSearchParams({
          page: page.toString(),
          limit: pagination.limit.toString(),
        });

        if (searchTerm) params.append("search", searchTerm);
        if (selectedCity) params.append("city", selectedCity);
        if (selectedPincode) params.append("pincode", selectedPincode);

        const response = await fetch(
          `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/user/outlets?${params}`
        );

        if (!response.ok) {
          throw new Error("Failed to fetch outlets");
        }

        const data = await response.json();

        if (data.success) {
          setOutlets(data.data);
          setPagination(data.pagination);

          // Extract unique cities for filter dropdown
          const cities = [
            ...new Set(
              data.data.map((outlet: OutletWithDistance) => outlet.city)
            ),
          ].filter(Boolean) as string[];
          setAvailableCities(cities);
        } else {
          toast.error("Failed to load outlets");
        }
      } catch (error) {
        console.error("Error fetching outlets:", error);
        toast.error("Error loading outlets");
      } finally {
        setLoading(false);
      }
    },
    [searchTerm, selectedCity, selectedPincode, pagination.limit]
  );

  // Fetch recommendations
  const fetchRecommendations = useCallback(async () => {
    try {
      const params = new URLSearchParams();
      if (selectedCity) params.append("city", selectedCity);
      if (selectedPincode) params.append("pincode", selectedPincode);

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/user/outlets/recommendations?${params}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("user-token")}`,
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setRecommendations(data.data);
        }
      }
    } catch (error) {
      console.error("Error fetching recommendations:", error);
    }
  }, [selectedCity, selectedPincode]);

  useEffect(() => {
    fetchOutlets();
    fetchRecommendations();
  }, [selectedCity, selectedPincode, fetchOutlets, fetchRecommendations]);

  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      if (searchTerm !== "") {
        fetchOutlets(1);
      } else {
        fetchOutlets();
      }
    }, 500);

    return () => clearTimeout(delayedSearch);
  }, [searchTerm, fetchOutlets]);

  const handleSearch = () => {
    fetchOutlets(1);
  };

  const handleClearFilters = () => {
    setSearchTerm("");
    setSelectedCity("");
    setSelectedPincode("");
  };

  const handlePageChange = (newPage: number) => {
    fetchOutlets(newPage);
  };

  const OutletCard = ({ outlet }: { outlet: OutletWithDistance }) => (
    <Card className="hover:shadow-lg transition-shadow duration-200">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg font-semibold">
              {outlet.name}
            </CardTitle>
            <p className="text-sm text-gray-600 mt-1">
              {outlet.foodChain.name}
            </p>
          </div>
          <Badge
            variant={outlet.status === "active" ? "default" : "secondary"}
            style={{
              backgroundColor:
                outlet.status === "active"
                  ? outlet.foodChain.theme.primaryColor
                  : undefined,
            }}
          >
            {outlet.status === "active" ? "Open" : "Closed"}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex items-start gap-2">
          <MapPin className="h-4 w-4 text-gray-500 mt-0.5 flex-shrink-0" />
          <div className="text-sm text-gray-600">
            <p>{outlet.address}</p>
            <p className="font-medium">
              {outlet.city}, {outlet.pincode}
            </p>
          </div>
        </div>

        {outlet.contact && (
          <div className="flex items-center gap-2">
            <Phone className="h-4 w-4 text-gray-500" />
            <span className="text-sm text-gray-600">{outlet.contact}</span>
          </div>
        )}

        <div className="flex items-center gap-4 text-sm text-gray-600">
          {outlet.isCloudKitchen && (
            <div className="flex items-center gap-1">
              <Truck className="h-4 w-4" />
              <span>Cloud Kitchen</span>
            </div>
          )}

          {outlet.deliveryRadius && (
            <div className="flex items-center gap-1">
              <MapPin className="h-4 w-4" />
              <span>{outlet.deliveryRadius}km delivery</span>
            </div>
          )}
        </div>

        <Separator />

        <div className="flex justify-between items-center">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              // Navigate to outlet menu
              window.location.href = `/menu?outlet=${outlet._id}`;
            }}
          >
            View Menu
          </Button>

          <Button
            size="sm"
            style={{ backgroundColor: outlet.foodChain.theme.primaryColor }}
            onClick={() => {
              // Navigate to order page
              window.location.href = `/order?outlet=${outlet._id}`;
            }}
          >
            Order Now
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="container mx-auto px-4 py-6 max-w-7xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">Find Outlets Near You</h1>
        <p className="text-gray-600">
          Discover restaurants and cloud kitchens in your area
        </p>
      </div>

      {/* Search and Filters */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search outlets..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={selectedCity} onValueChange={setSelectedCity}>
              <SelectTrigger>
                <SelectValue placeholder="Select City" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Cities</SelectItem>
                {availableCities.map((city) => (
                  <SelectItem key={city} value={city}>
                    {city}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Input
              placeholder="Enter pincode"
              value={selectedPincode}
              onChange={(e) => setSelectedPincode(e.target.value)}
            />

            <div className="flex gap-2">
              <Button onClick={handleSearch} className="flex-1">
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
              <Button variant="outline" onClick={handleClearFilters}>
                Clear
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recommendations Section */}
      {recommendations.length > 0 && (
        <div className="mb-8">
          <h2 className="text-2xl font-semibold mb-4 flex items-center gap-2">
            <Star className="h-5 w-5 text-yellow-500" />
            Recommended for You
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {recommendations.slice(0, 6).map((outlet) => (
              <OutletCard key={outlet._id} outlet={outlet} />
            ))}
          </div>
          <Separator className="my-6" />
        </div>
      )}

      {/* All Outlets Section */}
      <div className="mb-6">
        <h2 className="text-2xl font-semibold mb-4">
          All Outlets {selectedCity && `in ${selectedCity}`}
          <span className="text-sm font-normal text-gray-500 ml-2">
            ({pagination.total} found)
          </span>
        </h2>
      </div>

      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : outlets.length > 0 ? (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
            {outlets.map((outlet) => (
              <OutletCard key={outlet._id} outlet={outlet} />
            ))}
          </div>

          {/* Pagination */}
          {pagination.pages > 1 && (
            <div className="flex justify-center items-center gap-2">
              <Button
                variant="outline"
                disabled={!pagination.hasPrev}
                onClick={() => handlePageChange(pagination.page - 1)}
              >
                Previous
              </Button>

              <span className="text-sm text-gray-600">
                Page {pagination.page} of {pagination.pages}
              </span>

              <Button
                variant="outline"
                disabled={!pagination.hasNext}
                onClick={() => handlePageChange(pagination.page + 1)}
              >
                Next
              </Button>
            </div>
          )}
        </>
      ) : (
        <Card>
          <CardContent className="text-center py-12">
            <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No outlets found</h3>
            <p className="text-gray-600 mb-4">
              Try adjusting your search criteria or location filters
            </p>
            <Button onClick={handleClearFilters}>Clear Filters</Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default OutletsPage;
